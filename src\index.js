console.clear();
require('dotenv').config({ quiet: true });
import { GatewayIntentBits } from "discord.js";
import Client from "./structures/Client";
import { connectToDatabase } from "./utils/database/main";
import loadCommands from "./utils/general/loadCommands";
import loadEvents from "./utils/general/loadEvents";

const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildModeration
    ]
})

await connectToDatabase();

loadEvents(client);
loadCommands(client);

client.login(process.env.BOT_TOKEN)