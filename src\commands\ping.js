import { EmbedBuilder, SlashCommandBuilder } from "discord.js";
import Command from "../structures/Command";

export default new Command({
    data: new SlashCommandBuilder()
        .setName('ping')
        .setDescription('Test the bot\'s response time.'),
    cooldown: 10,
    execute(client, interaction) {

        const pinging = new EmbedBuilder()
            .setColor('Yellow')
            .setDescription('Testing response time...');

        interaction.reply({ embeds: [pinging] }).then(response => {

            const ping = Date.now() - response.createdTimestamp;

            const embed = new EmbedBuilder()
                .setColor('Random')
                .addFields({ name: `${client.emoji('ws')} ` + 'WS Latency', value: `${Math.abs(client.ws.ping).toLocaleString()} ms`, inline: true })
                .addFields({ name: '\u200b', value: '\u200b', inline: true })
                .addFields({ name: `${client.emoji('discord')} ` + 'Response Time', value: `${ping.toLocaleString()} ms`, inline: true })
                .setTimestamp()

            interaction.editReply({ embeds: [embed] })
        })
    }
})