import { JsonDatabase } from "dev.db";
import { Collection, Client as DiscordClient, EmbedBuilder } from "discord.js";
import { existsSync, readFileSync, writeFileSync } from "fs";
import path from "path";

export default class Client extends DiscordClient {

    /** @param {import("discord.js").ClientOptions?} options */
    constructor(options) {
        super(options);

        /** @type {import("discord.js").Collection<string, import("./structures/Command").default>} */
        this.commands = new Collection();

        /** @type {import("discord.js").Collection<string, number>} */
        this.cooldowns = new Collection();

        /** @type {JsonDatabase} */
        this.db = new JsonDatabase({ autoSave: true, dataFile: 'database.json' })

        const configPath = path.join(process.cwd(), 'config.json');

        if (!existsSync(configPath)) {
            writeFileSync(configPath, JSON.stringify({ owners: [], skipOwnerCheck: false }, null, 4));
        }

        /** @type {import('../../config.json')} */
        this.config = JSON.parse(readFileSync(configPath, 'utf-8'));
    }

    /** @param {string} k */
    getCooldown(k) {
        const v = this.cooldowns.get(k);
        if (v && v <= Date.now()) {
            this.cooldowns.delete(k);
            return null;
        }
        return v;
    }

    /**
     * @param {string} k 
     * @param {number} v 
     */
    setCooldown(k, v) {
        this.cooldowns.set(k, v);
    }

    /** @param {string} name */
    emoji(name) {
        return this.application.emojis.cache.find(e => e.name === name);
    }

    /**
     * 
     * @param {'perm' | 'err' | 'cooldown'} type 
     * @param  {...string} args 
     */
    embed(type, ...args) {
        if (type == 'perm') {
            return new EmbedBuilder()
                .setColor('Orange')
                .setAuthor({ iconURL: this.emoji('warning_shield').imageURL(), name: 'Missing Permissions' })
                .setDescription(args[0] || 'You are not allowed to use this command.')
        }
        if (type == 'err') {
            return new EmbedBuilder()
                .setColor('Red')
                .setAuthor({ iconURL: this.emoji('error').imageURL(), name: 'Error' })
                .setDescription(args[0])
        }
        if (type == 'cooldown') {
            return new EmbedBuilder()
                .setColor('Grey')
                .setAuthor({ iconURL: this.emoji('cooldown').imageURL(), name: 'Cooldown' })
                .setDescription(`Please try again <t:${Math.floor(args[0] / 1000)}:R>.`)
        }
    }

    cmd(name){
        return `</${name}:${this.application.commands.cache.find(c => c.name === name).id}>`
    }
}