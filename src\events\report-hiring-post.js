import { MessageFlags } from "discord.js";
import Event from "../structures/Event";
import { getHiringPost, updateHiringPost } from "../utils/database/main";

export default new Event({
    name: 'interactionCreate',
    async execute(client, interaction) {
        if (!interaction.isButton()) return;
        if (interaction.customId !== 'report-hiring-post') return;

        await interaction.deferReply({ flags: MessageFlags.Ephemeral })

        const messageId = interaction.message.id
        const post = await getHiringPost({ messageId });

        if (!post) {
            return interaction.editReply({ embeds: [client.embed('err', 'This post does not exist.')] })
        }
        if (post.reports.includes(interaction.user.id)) {
            return interaction.editReply({ embeds: [client.embed('err', 'You have already reported this post.')] })
        }
        if (post.status==='SUSPENDED') {
            return interaction.editReply({ embeds: [client.embed('err', 'This post has already been suspended. Are you sure you want to continue?')] })
        }

        await report();

        async function report() {

            await updateHiringPost({ messageId }, { $push: [interaction.user.id] });

        }
    }
})