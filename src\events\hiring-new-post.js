import { ActionRowBuilder, MessageFlags, ModalBuilder, TextInputBuilder, TextInputStyle } from "discord.js";
import Event from "../structures/Event";
import { getUser } from "../utils/database/main";

export default new Event({
    name: 'interactionCreate',
    async execute(client, interaction) {
        if (!interaction.isButton()) return;
        if (interaction.customId !== 'hiring-new-post') return;

        const user = await getUser(interaction.user.id);

        if (user.suspended) {
            return interaction.reply({ embeds: [client.embed('err', 'Your account has been suspended. You are not allowed to use forum channels until moderators review your case.')], flags: MessageFlags.Ephemeral })
        }

        const modal = new ModalBuilder()
            .setCustomId('hiring:first-modal')
            .setTitle('🧑‍💼 Create a Hiring Post')
            .addComponents(
                new ActionRowBuilder()
                    .addComponents(
                        new TextInputBuilder()
                            .setStyle(TextInputStyle.Short)
                            .setCustomId('header')
                            .setRequired(false)
                            .setLabel('Header Image Link')
                            .setPlaceholder('e.g. https://imgur.com/a/zldF3kH')
                    ),
                new ActionRowBuilder()
                    .addComponents(
                        new TextInputBuilder()
                            .setStyle(TextInputStyle.Short)
                            .setCustomId('title')
                            .setLabel('Title')
                            .setRequired(true)
                            .setMinLength(5)
                            .setMaxLength(40)
                            .setPlaceholder('e.g. Looking for a developer')
                    ),
                new ActionRowBuilder()
                    .addComponents(
                        new TextInputBuilder()
                            .setStyle(TextInputStyle.Paragraph)
                            .setCustomId('description')
                            .setLabel('Details')
                            .setRequired(true)
                            .setMinLength(10)
                            .setPlaceholder('Job, requirements, budget, contact info... (supports markdown)')
                    ),
                new ActionRowBuilder()
                    .addComponents(
                        new TextInputBuilder()
                            .setStyle(TextInputStyle.Paragraph)
                            .setCustomId('images')
                            .setLabel('Image Links')
                            .setRequired(false)
                            .setPlaceholder('Enter image links line by line:\n\nhttps://link1.com\nhttps://link2.com')
                    )
            )

        interaction.showModal(modal);

    }
})