import chalk from "chalk";
import Event from "../structures/Event";
import log from "../utils/general/log";
import { ActivityType } from "discord.js";

export default new Event({
    name: 'ready',
    async execute(client) {

        log('ok', `Logged in as ${chalk.cyanBright(client.user.tag)}`);

        client.user.setStatus('idle');
        client.user.setActivity({ type: ActivityType.Watching, name: '❤️ alperdev' })

        const commands = client.commands.map(c => c.data)
        await client.application.commands.set(commands)
        log('ok', `${chalk.yellowBright(commands.length)} ${commands.length > 1 ? 'commands have' : 'command has'} been registered.`);

        const emojis = await client.application.emojis.fetch();
        log('ok', `${chalk.yellowBright(emojis.size)} ${emojis.size > 1 ? 'emojis have' : 'emoji has'} been fetched.`);
        
    }
})