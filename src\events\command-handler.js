import chalk from "chalk";
import Event from "../structures/Event";
import log from "../utils/general/log";
import { MessageFlags } from "discord.js";

export default new Event({
    name: 'interactionCreate',
    async execute(client, interaction) {

        if (!interaction.isChatInputCommand()) return;

        /** @type {import('../structures/Command').default?} */
        const command = client.commands.get(interaction.commandName);
        if (!command) {
            return interaction.reply({ components: [client.embed('err', 'This command is no longer available.')], flags: MessageFlags.Ephemeral });
        }

        if (command.ownersOnly && !client.config.owners.includes(interaction.user.id)) {
            return interaction.reply({ components: [client.embed('perm', 'This command can only be used by the bot owners.')], flags: MessageFlags.Ephemeral });
        }

        if (command.cooldown) {
            const cooldown = client.getCooldown(`${interaction.commandName}.${interaction.user.id}`);
            if (cooldown) {
                return interaction.reply({ components: [client.embed('cooldown', cooldown)], flags: MessageFlags.Ephemeral });
            }

            client.setCooldown(`${interaction.commandName}.${interaction.user.id}`, interaction.createdTimestamp + command.cooldown);
        }

        try {
            command.execute(client, interaction, interaction.locale);
        } catch (error) {
            log('e', `Error executing the command ${chalk.redBright(interaction.commandName)}:`);
            console.log(error.stack)
        }
    }
})