import { ActionRow<PERSON>uilder, <PERSON>tonBuilder, ButtonStyle, ChannelSelectMenuBuilder, ChannelType, ContainerBuilder, MessageFlags, PermissionFlagsBits, SectionBuilder, SlashCommandBuilder, TextDisplayBuilder } from "discord.js";
import Command from "../structures/Command";

export default new Command({
    data: new SlashCommandBuilder()
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .setName('setlogs')
        .setDescription('Set log channels.'),
    cooldown: 10,
    async execute(client, interaction) {

        function getContainers(timeout = false) {
            const mainLog = client.db.get('settings.channels.mainLog');
            const banLog = client.db.get('settings.channels.banLog');
            const kickLog = client.db.get('settings.channels.kickLog');
            const warnLog = client.db.get('settings.channels.warnLog');

            const container1 = new ContainerBuilder()
                .addSectionComponents(
                    new SectionBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent(`
## ${client.emoji('log')} Main Log Channel

All moderation logs will be sent to this channel.
`))
                        .setButtonAccessory(
                            new ButtonBuilder()
                                .setCustomId('disable:mainLog')
                                .setStyle(ButtonStyle.Danger)
                                .setLabel('Disable')
                                .setDisabled(timeout || !mainLog)
                        )
                )
                .addActionRowComponents(new ActionRowBuilder().addComponents(
                    new ChannelSelectMenuBuilder()
                        .setChannelTypes(ChannelType.GuildText)
                        .setCustomId('set:mainLog')
                        .setMaxValues(1)
                        .setDefaultChannels(mainLog || [])
                        .setDisabled(timeout)
                ))

            const container2 = new ContainerBuilder()
                .addSectionComponents(
                    new SectionBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent(`
## ${client.emoji('ban_hammer')} Ban Log Channel

All ban logs will be sent to this channel.
`))
                        .setButtonAccessory(
                            new ButtonBuilder()
                                .setCustomId('disable:banLog')
                                .setStyle(ButtonStyle.Danger)
                                .setLabel('Disable')
                                .setDisabled(timeout || !banLog)
                        )
                )
                .addActionRowComponents(new ActionRowBuilder().addComponents(
                    new ChannelSelectMenuBuilder()
                        .setChannelTypes(ChannelType.GuildText)
                        .setCustomId('set:banLog')
                        .setMaxValues(1)
                        .setDefaultChannels(banLog || [])
                        .setDisabled(timeout)
                ))

            const container3 = new ContainerBuilder()
                .addSectionComponents(
                    new SectionBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent(`
## ${client.emoji('kicked')} Kick Log Channel

All kick logs will be sent to this channel.
`))
                        .setButtonAccessory(
                            new ButtonBuilder()
                                .setCustomId('disable:kickLog')
                                .setStyle(ButtonStyle.Danger)
                                .setLabel('Disable')
                                .setDisabled(timeout || !kickLog)
                        )
                )
                .addActionRowComponents(new ActionRowBuilder().addComponents(
                    new ChannelSelectMenuBuilder()
                        .setChannelTypes(ChannelType.GuildText)
                        .setCustomId('set:kickLog')
                        .setMaxValues(1)
                        .setDefaultChannels(kickLog || [])
                        .setDisabled(timeout)
                ))

            const container4 = new ContainerBuilder()
                .addSectionComponents(
                    new SectionBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent(`
## ${client.emoji('warn')} Warn Log Channel

All warn logs will be sent to this channel.
`))
                        .setButtonAccessory(
                            new ButtonBuilder()
                                .setCustomId('disable:warnLog')
                                .setStyle(ButtonStyle.Danger)
                                .setLabel('Disable')
                                .setDisabled(timeout || !warnLog)
                        )
                )
                .addActionRowComponents(new ActionRowBuilder().addComponents(
                    new ChannelSelectMenuBuilder()
                        .setChannelTypes(ChannelType.GuildText)
                        .setCustomId('set:warnLog')
                        .setMaxValues(1)
                        .setDefaultChannels(warnLog || [])
                        .setDisabled(timeout)
                ))

            return [container1, container2, container3, container4]
        }

        interaction.reply({ components: getContainers(), flags: MessageFlags.IsComponentsV2, withResponse: false }).then(response => {

            const collector = response.createMessageComponentCollector({ time: 5 * 60 * 1000 })

            collector.on('collect', collected => {
                const [action, type] = collected.customId.split(':');
                if (collected.isButton() && action == 'disable') {
                    client.db.set(`settings.channels.${type}`, null);
                    collected.update({ components: getContainers() })
                }
                if (collected.isChannelSelectMenu() && action == 'set') {
                    client.db.set(`settings.channels.${type}`, collected.values[0]);
                    collected.update({ components: getContainers() })
                }
            })

            collector.on('end', () => {
                response.edit({ components: getContainers(true) })
            })
        })
    }
})