import { ActionRow<PERSON>uilder, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>le, ContainerBuilder, EmbedBuilder, MessageFlags, PermissionFlagsBits, SlashCommandBuilder, TextDisplayBuilder } from "discord.js";
import Command from "../structures/Command";
import { newCase } from "../utils/database/main";
import delay from "delay";
import moment from "moment";
import 'moment-duration-format'

export default new Command({
    data: new SlashCommandBuilder()
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .setName('unban-all')
        .setDescription('Revoke all bans.')
        .addStringOption(option => option
            .setName('reason')
            .setDescription('reason to revoke bans')
        ),
    async execute(client, interaction) {

        const bans = await interaction.guild.bans.fetch().then(b => Array.from(b.values())).catch(() => 'error')

        if (bans === 'error') {
            return interaction.reply({ embeds: [client.embed('err', 'Failed to fetch bans. Please try again later.')], flags: MessageFlags.Ephemeral })
        }

        if (!bans.length) {
            return interaction.reply({ embeds: [client.embed('err', 'There is no ban to revoke.')], flags: MessageFlags.Ephemeral })
        }

        let unbanned = 0;
        const progressBar = () => {
            const totalBars = 10;
            const filledCount = Math.round((unbanned / bans.length) * totalBars);
            let bar = '';
            for (let i = 0; i < totalBars; i++) {
                const isFilled = i < filledCount;
                if (i === 0) {
                    bar += isFilled ? client.emoji('start_filled').toString() : client.emoji('start_unfilled').toString()
                } else if (i === totalBars - 1) {
                    bar += isFilled ? client.emoji('end_filled').toString() : client.emoji('end_unfilled').toString()
                } else {
                    bar += isFilled ? client.emoji('mid_filled').toString() : client.emoji('mid_unfilled').toString()
                }
            }
            return bar
        };


        const preEmbed = new EmbedBuilder()
            .setColor('Yellow')
            .setAuthor({ name: 'Are you sure?', iconURL: client.emoji('warning_shield').imageURL() })
            .setDescription(`You are about to revoke **${bans.length.toLocaleString()}** ${bans.length > 1 ? 'bans' : 'ban'}. This action cannot be undone.`);

        const buttons = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('cancel')
                .setStyle(ButtonStyle.Success)
                .setLabel('Cancel'),
            new ButtonBuilder()
                .setCustomId('confirm')
                .setStyle(ButtonStyle.Danger)
                .setLabel('Confirm')
        )

        interaction.reply({ embeds: [preEmbed], components: [buttons], withResponse: false }).then(async response => {

            const collector = response.createMessageComponentCollector({ time: 60_000 })

            collector.on('collect', async collected => {
                if (collected.user.id !== interaction.user.id) {
                    return collected.reply({ embeds: [client.embed('err', 'You can\'t interact with this button.')], flags: MessageFlags.Ephemeral })
                }
                collector.stop();

                if (collected.customId === 'confirm') {
                    const reason = interaction.options.getString('reason')

                    const update = async () => {
                        const embed = new EmbedBuilder()
                            .setColor('Grey')
                            .setAuthor({ name: 'Revoking All Bans', iconURL: client.emoji('unban_hammer').imageURL() })
                            .setDescription(`${progressBar()} ${(unbanned / bans.length * 100).toFixed(1)}%`)
                            .addFields({ name: 'Revoked', value: unbanned.toString(), inline: true })
                            .addFields({ name: 'Bans Left', value: (bans.length - unbanned).toLocaleString(), inline: true })
                            .addFields({ name: 'Started At', value: `<t:${Math.floor(collected.createdTimestamp / 1000)}:R>`, inline: true })
                            .addFields({ name: 'Reason', value: reason || 'No reason provided.', inline: true })

                        await interaction.editReply({ embeds: [embed], components: [] })
                    }

                    await update();

                    const interval = setInterval(update, 3000);

                    for await (const ban of bans) {
                        await interaction.guild.bans.remove(ban.user, reason);
                        unbanned++;
                        await delay(Math.floor(Math.random() * 2000))
                    }
                    clearInterval(interval)
                    const now = Date.now();

                    const _case = await newCase({ moderatorId: interaction.user.id, type: 'unban-all', reason, createdTimestamp: collected.createdTimestamp, extra: { users: bans.map(b => b.user.id) } })

                    const elapsedTime = moment.duration(now - collected.createdTimestamp).format({ trim: 'all' })

                    const embed = new EmbedBuilder()
                        .setColor('Grey')
                        .setAuthor({ name: 'All Bans Revoked', iconURL: client.emoji('unban_hammer').imageURL() })
                        .addFields({ name: 'Case No', value: `#${_case.id.toString().padStart(4, '0')}`, inline: true })
                        .addFields({ name: 'Responsible Moderator', value: interaction.user.toString(), inline: true })
                        .addFields({ name: 'Revoked Ban Count', value: unbanned.toLocaleString(), inline: true })
                        .addFields({ name: 'Elapsed Time', value: elapsedTime, inline: true })
                        .addFields({ name: 'Reason', value: reason || 'No reason provided.', inline: true })
                        .setTimestamp(collected.createdTimestamp)

                    await interaction.editReply({ embeds: [embed] })

                    const mainLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.mainLog'));
                    const banLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.banLog'));

                    if (mainLog) {
                        mainLog.send({ embeds: [embed] })
                    }
                    if (banLog) {
                        banLog.send({ embeds: [embed] })
                    }
                }
                if (collected.customId === 'cancel') {

                    const cancelled = new ContainerBuilder().addTextDisplayComponents(new TextDisplayBuilder().setContent(`${client.emoji('cross')} You cancelled this action.`));
                    interaction.editReply({ embeds: [], components: [cancelled], flags: MessageFlags.IsComponentsV2 })

                }
            })

            collector.on('end', (reason) => {
                if (reason !== 'time') return;

                const cancelled = new ContainerBuilder().addTextDisplayComponents(new TextDisplayBuilder().setContent(`${client.emoji('cross')} This action was automatically cancelled.`));
                interaction.editReply({ embeds: [], components: [cancelled], flags: MessageFlags.IsComponentsV2 }).catch(console.log)
            })
        })
    }
})