import mongoose from "mongoose"
import log from "../general/log";
import chalk from "chalk";
import Case from "./models/Case";
import User from "./models/User";
import HiringPost from "./models/HiringPost";

export const connectToDatabase = async () => {
    const uri = process.env.MONGODB_URI;
    if (!uri) return;
    try {
        await mongoose.connect(uri, { retryWrites: true, writeConcern: { w: 'majority' } });
        log('ok', 'Connected to MongoDB.');
    } catch (error) {
        log('e', `Failed to connect to MongoDB: ${chalk.redBright(error.message)}`);
        process.exit(1);
    }
}

/** @param {{ type: string; userId: string; moderatorId: string; reason?: string; createdTimestamp: number; endsTimestamp?: number; duration?: number; }} options */
export const newCase = async options => {
    options.id = await Case.countDocuments() + 1;
    const _case = await Case.create(options);
    return _case;
}

export const getUser = async (userId) => {
    const user = await User.findOne({ userId });
    if (!user) {
        const newUser = await User.create({ userId });
        return newUser
    }
    return user;
}


/** @param {{ ownerId?: string; channelId?: string; messageId: string; }} filter */
export const getHiringPost = async (filter) => {
    const posts = await HiringPost.findOne(filter);
    return posts;
}

/** @param {{ ownerId?: string; status?: 'OPEN' | 'CLOSED' | 'SUSPENDED'; channelId?: string; }} filter */
export const getHiringPosts = async (filter) => {
    const posts = await HiringPost.find(filter);
    return posts;
}

/**
 * @param {object} options
 * @param {string} options.ownerId
 * @param {string} options.channelId
 * @param {string} options.messageId
 * @param {string} options.title
 * @param {string} options.description
 * @param {number} options.createdTimestamp
 */
export const createHiringPost = async (options) => {
    const post = await HiringPost.create({ ...options, reports: [], status: 'OPEN' });
    return post;
}

export const updateHiringPost = async (filter, update) => {
    const post = await HiringPost.findOneAndUpdate(filter, update);
    return post;
}