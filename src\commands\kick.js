import { ContainerBuilder, EmbedBuilder, MessageFlags, PermissionFlagsBits, SlashCommandBuilder, TextDisplayBuilder } from "discord.js";
import Command from "../structures/Command";
import { newCase } from "../utils/database/main";

export default new Command({
    data: new SlashCommandBuilder()
        .setDefaultMemberPermissions(PermissionFlagsBits.KickMembers)
        .setName('kick')
        .setDescription('Kick a user.')
        .addUserOption(option => option
            .setName('user')
            .setDescription('user to be kicked')
            .setRequired(true)
        )
        .addStringOption(option => option
            .setName('reason')
            .setDescription('reason for the kick')
        ),
    async execute(client, interaction) {

        const user = interaction.options.getUser('user', true);

        if (user.id === interaction.user.id) {
            return interaction.reply({ embeds: [client.embed('err', 'You can\'t kick yourself.')], flags: MessageFlags.Ephemeral })
        }

        if (user.id === interaction.guild.ownerId) {
            return interaction.reply({ embeds: [client.embed('perm', 'You can\'t kick the server owner.')], flags: MessageFlags.Ephemeral })
        }

        if (user.id === client.user.id) {
            return interaction.reply({ embeds: [client.embed('err', 'I can\'t kick myself. But why would you want to do that? 🥲')], flags: MessageFlags.Ephemeral })
        }

        const member = await interaction.guild.members.fetch({ user, force: true }).catch(() => null);

        if (!member) {
            return interaction.reply({ embeds: [client.embed('err', 'That user is not in the server.')], flags: MessageFlags.Ephemeral })
        }

        if (member && member.roles.highest.position >= interaction.member.roles.highest.position && interaction.member.id !== interaction.guild.ownerId) {
            return interaction.reply({ embeds: [client.embed('perm', 'You can\'t kick someone with a higher or equal role to yours.')], flags: MessageFlags.Ephemeral })
        }

        if (member && !member.kickable) {
            return interaction.reply({ embeds: [client.embed('err', `I am not able to kick that user. Please move ${interaction.guild.roles.botRoleFor(client.user.id)} role to the top.`)], flags: MessageFlags.Ephemeral })
        }

        const reason = interaction.options.getString('reason');

        await member.kick(reason || 'No reason provided.');
        const now = Date.now()
        const time = now - interaction.createdTimestamp

        const _case = await newCase({
            userId: user.id,
            moderatorId: interaction.user.id,
            type: 'kick',
            reason,
            createdTimestamp: interaction.createdTimestamp
        })

        const container = new ContainerBuilder().addTextDisplayComponents(new TextDisplayBuilder().setContent(
            `${client.emoji('kicked')} ${user} has been kicked from the server.${reason ? `\n\n**${client.emoji('pen')} Reason:** ${reason}` : ''}`
        ))

        interaction.reply({ components: [container], flags: MessageFlags.IsComponentsV2 })

        const logEmbed = new EmbedBuilder()
            .setColor('Grey')
            .setAuthor({ name: 'User Kicked', iconURL: client.emoji('kicked').imageURL() })
            .setThumbnail(member.displayAvatarURL())
            .setTimestamp(now)
            .setFooter({ text: `This action took ${time.toLocaleString()} ms.` })
            .addFields({ name: 'Case No', value: `#${_case.id.toString().padStart(4, '0')}`, inline: true })
            .addFields({ name: 'Kicked User', value: user.toString(), inline: true })
            .addFields({ name: 'Responsible Moderator', value: interaction.user.toString(), inline: true })
            .addFields({ name: 'Reason', value: reason ?? 'No reason provided.', inline: true })

        const mainLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.mainLog'));
        const kickLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.kickLog'));

        if (mainLog) {
            mainLog.send({ embeds: [logEmbed] })
        }
        if (kickLog) {
            kickLog.send({ embeds: [logEmbed] })
        }

    }
})