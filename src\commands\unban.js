import { EmbedBuilder, MessageFlags, PermissionFlagsBits, SlashCommandBuilder } from "discord.js";
import Command from "../structures/Command";
import { newCase } from "../utils/database/main";

export default new Command({
    data: new SlashCommandBuilder()
        .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers)
        .setName('unban')
        .setDescription('Revoke a user\'s ban.')
        .addUserOption(option => option
            .setName('user')
            .setDescription('user to be unbanned')
            .setRequired(true)
        )
        .addStringOption(option => option
            .setName('reason')
            .setDescription('reason to revoke ban')
        ),
    async execute(client, interaction) {

        const user = interaction.options.getUser('user', true);
        const banned = await interaction.guild.bans.fetch(user).catch(() => null);

        if (!banned) {
            return interaction.reply({ embeds: [client.embed('err', 'That user is not banned.')], flags: MessageFlags.Ephemeral })
        }

        const reason = interaction.options.getString('reason');

        await interaction.guild.bans.remove(user, { reason: reason || 'No reason provided.' });
        const now = Date.now()
        const time = (now - interaction.createdTimestamp).toLocaleString();

        const _case = await newCase({
            userId: user.id,
            moderatorId: interaction.user.id,
            type: 'unban',
            reason,
            createdTimestamp: interaction.createdTimestamp
        })

        const embed = new EmbedBuilder()
            .setColor('Grey')
            .setAuthor({ name: 'Ban Revoked', iconURL: client.emoji('unban_hammer').imageURL() })
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp(now)
            .setFooter({ text: `This action took ${time} ms.` })
            .addFields({ name: 'Case No', value: `#${_case.id.toString().padStart(4, '0')}`, inline: true })
            .addFields({ name: 'User', value: user.toString(), inline: true })
            .addFields({ name: 'Responsible Moderator', value: interaction.user.toString(), inline: true })
            .addFields({ name: 'Reason', value: reason ?? 'No reason provided.', inline: true })

        interaction.reply({ embeds: [embed] })

        const mainLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.mainLog'));
        const banLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.banLog'));

        if (mainLog) {
            mainLog.send({ embeds: [embed] })
        }
        if (banLog) {
            banLog.send({ embeds: [embed] })
        }

    }
})