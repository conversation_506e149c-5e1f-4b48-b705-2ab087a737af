import { EmbedBuilder, MessageFlags, PermissionFlagsBits, SlashCommandBuilder } from "discord.js";
import Command from "../structures/Command";
import { newCase } from "../utils/database/main";

export default new Command({
    data: new SlashCommandBuilder()
        .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers)
        .setName('banx')
        .setDescription('Ban a user quickly. (2x faster than /ban)')
        .addUserOption(option => option
            .setName('user')
            .setDescription('user to be banned')
            .setRequired(true)
        ),
    async execute(client, interaction) {

        try {

            const user = interaction.options.getUser('user', true);

            if (user.id === interaction.user.id) {
                return interaction.reply({ embeds: [client.embed('err', 'You can\'t ban yourself.')], flags: MessageFlags.Ephemeral })
            }

            const member = interaction.guild.members.cache.get(user.id) || await interaction.guild.members.fetch(user).catch(() => null);

            if (member && member.roles.highest.position >= interaction.member.roles.highest.position && interaction.member.id !== interaction.guild.ownerId) {
                return interaction.reply({ embeds: [client.embed('perm', 'You can\'t ban someone with a higher or equal role to yours.')], flags: MessageFlags.Ephemeral })
            }

            const reason = 'Quick Ban';

            await interaction.guild.bans.create(user, { reason });
            const now = Date.now()
            const time = now - interaction.createdTimestamp

            const _case = await newCase({
                userId: user.id,
                moderatorId: interaction.user.id,
                type: 'ban',
                reason,
                createdTimestamp: now
            })

            const embed = new EmbedBuilder()
                .setColor('Red')
                .setAuthor({ name: (member || user).displayName, iconURL: (member || user).displayAvatarURL() })
                .setDescription(`${client.emoji('ban_hammer')} ${user} has been banned from the server.`)
                .setFooter({ text: `This action took ${time} ms.` })
                .setTimestamp(now)

            interaction.reply({ embeds: [embed] })

            const logEmbed = new EmbedBuilder()
                .setColor('Red')
                .setAuthor({ name: 'User Banned', iconURL: client.emoji('ban_hammer').imageURL() })
                .setThumbnail(user.displayAvatarURL())
                .setTimestamp(now)
                .setFooter({ text: `This action took ${time} ms.` })
                .addFields({ name: 'Case No', value: `#${_case.id.toString().padStart(4, '0')}`, inline: true })
                .addFields({ name: 'Banned User', value: user.toString(), inline: true })
                .addFields({ name: 'Responsible Moderator', value: interaction.user.toString(), inline: true })
                .addFields({ name: 'Duration', value: 'Indefinite', inline: true })
                .addFields({ name: 'Ends At', value: 'Never', inline: true })
                .addFields({ name: 'Reason', value: reason, inline: true })

            const mainLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.mainLog'));
            const banLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.banLog'));

            if (mainLog) {
                mainLog.send({ embeds: [logEmbed] })
            }
            if (banLog) {
                banLog.send({ embeds: [logEmbed] })
            }

        } catch {
            interaction.reply({ embeds: [client.embed('err', `Failed to ban that user. Please use ${client.cmd('ban')} instead.`)], flags: MessageFlags.Ephemeral })
        }

    }
})