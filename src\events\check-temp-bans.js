import Event from "../structures/Event";
import revokeTempBan from "../utils/general/revokeTempBan";

export default new Event({
    name: 'ready',
    async execute(client) {

        setInterval(() => {
            const tempBans = client.db.get('tempBans') || [];

            for (const data of tempBans) {
                if (Date.now() < data.endsTimestamp) continue;
                revokeTempBan(client, data);
            }
        }, 30_000);
    }
})