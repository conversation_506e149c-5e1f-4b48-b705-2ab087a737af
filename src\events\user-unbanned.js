import Event from "../structures/Event";

export default new Event({
    name: 'guildBanRemove',
    async execute(client, ban) {

        const tempBans = client.db.get('tempBans') || [];
        const data = tempBans.find(d => d.userId == ban.user.id && d.guildId == ban.guild.id);
        if (!data) return;

        client.db.set('tempBans', tempBans.filter(d => d.userId !== ban.user.id && d.guildId !== ban.guild.id))

    }
})