import { ActionRowBuilder, ButtonBuilder, ButtonStyle, ContainerBuilder, EmbedBuilder, MediaGalleryBuilder, MediaGalleryItemBuilder, MessageFlags, SeparatorBuilder, SeparatorSpacingSize, TextDisplayBuilder } from "discord.js";
import Event from "../structures/Event";
import { createHiringPost, getHiringPosts, getUser } from "../utils/database/main";
import * as linkify from 'linkifyjs'

export default new Event({
    name: 'interactionCreate',
    async execute(client, interaction) {
        if (!interaction.isModalSubmit()) return;
        if (interaction.customId !== 'hiring:first-modal') return;

        await interaction.deferReply({ flags: MessageFlags.Ephemeral })

        const user = await getUser(interaction.user.id);

        if (user.suspended) {
            return interaction.editReply({ embeds: [client.embed('err', 'Your account has been suspended. You are not allowed to use forum channels until moderators review your case.')], flags: MessageFlags.Ephemeral })
        }

        const header = interaction.fields.getTextInputValue('header')
        const headerLink = header && linkify.find(header, 'url').at(0)?.href;

        if (header && !headerLink) {
            return interaction.editReply({ embeds: [client.embed('err', 'Invalid header image link. Valid example: **`https://imgur.com/a/zldF3kH`**')], flags: MessageFlags.Ephemeral })
        }

        const images = interaction.fields.getTextInputValue('images');
        const imageLinks = images ? linkify.find(images, 'url').map(data => data.href) : []

        if (images && !imageLinks.length) {
            return interaction.editReply({ embeds: [client.embed('err', 'Invalid image links. Valid example:```https://link1.com\nhttps://link2.com```')], flags: MessageFlags.Ephemeral })
        }

        if (imageLinks.length > 10) {
            return interaction.editReply({ embeds: [client.embed('err', 'You can only add up to 10 images.')], flags: MessageFlags.Ephemeral })
        }

        const title = interaction.fields.getTextInputValue('title');
        const description = interaction.fields.getTextInputValue('description');

        const hiringPosts = await getHiringPosts({ ownerId: interaction.user.id, status: 'OPEN' });

        if (hiringPosts.length) {

            const embed = new EmbedBuilder()
                .setAuthor({ name: 'Review Your Posts', iconURL: client.emoji('log').imageURL() })
                .setDescription(hiringPosts.map(post => `- ${post.title}`).join('\n'))
                .addFields({ name: 'Your New Post', value: title })
                .setFooter({ text: 'Check that you\'re not repeating a post you\'ve already made.', iconURL: client.emoji('warning_shield').imageURL() })

            const buttons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm')
                        .setStyle(ButtonStyle.Success)
                        .setLabel('Confirm'),
                    new ButtonBuilder()
                        .setCustomId('cancel')
                        .setStyle(ButtonStyle.Danger)
                        .setLabel('Cancel')
                )

            return interaction.editReply({ embeds: [embed], components: [buttons] }).then(message => {

                const collector = message.createMessageComponentCollector({
                    time: 60_000,
                    max: 1,
                    filter: i => i.user.id === interaction.user.id
                })

                collector.on('collect', collected => {
                    if (collected.customId == 'confirm') {
                        createPost()
                    } else {
                        interaction.editReply({ embeds: [client.embed('err', 'You cancelled this action.')], components: [] })
                    }
                })

                collector.on('end', reason => {
                    if (reason !== 'time') return;
                    interaction.editReply({ embeds: [client.embed('err', 'You did not respond in time. Please try again.')], components: [] })
                })
            })
        }

        createPost();

        async function createPost() {
            try {
                const container = new ContainerBuilder()

                if (headerLink) {
                    container
                        .addMediaGalleryComponents(
                            new MediaGalleryBuilder()
                                .addItems(
                                    new MediaGalleryItemBuilder()
                                        .setURL(headerLink)
                                )
                        )
                        .addSeparatorComponents(
                            new SeparatorBuilder()
                                .setSpacing(SeparatorSpacingSize.Large)
                        )
                }

                container
                    .addTextDisplayComponents(
                        new TextDisplayBuilder()
                            .setContent('## ' + title)
                    )
                    .addSeparatorComponents(
                        new SeparatorBuilder()
                            .setSpacing(SeparatorSpacingSize.Large)
                    )
                    .addTextDisplayComponents(
                        new TextDisplayBuilder()
                            .setContent(description)
                    )

                if (imageLinks.length) {
                    container
                        .addSeparatorComponents(
                            new SeparatorBuilder()
                                .setSpacing(SeparatorSpacingSize.Large)
                        )
                        .addMediaGalleryComponents(
                            new MediaGalleryBuilder()
                                .addItems(
                                    imageLinks.map(link => new MediaGalleryItemBuilder().setURL(link))
                                )
                        )
                }

                const buttons = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setStyle(ButtonStyle.Link)
                            .setURL(`https://discord.com/users/${interaction.user.id}`)
                            .setEmoji('✉️')
                            .setLabel('Contact Owner'),
                        new ButtonBuilder()
                            .setStyle(ButtonStyle.Secondary)
                            .setCustomId('report-hiring-post')
                            .setEmoji(client.emoji('error').id)
                            .setLabel('Report'),
                        new ButtonBuilder()
                            .setStyle(ButtonStyle.Secondary)
                            .setCustomId('settings-hiring-post')
                            .setEmoji(client.emoji('gear').id)
                            .setLabel('Settings')
                    )

                /** @type {import('discord.js').ForumChannel | undefined} */
                const forum = interaction.guild.channels.cache.get('1390415462948405350')
                const thread = await forum.threads.create({
                    name: title,
                    message: { components: [container, buttons], flags: MessageFlags.IsComponentsV2 }
                })

                await createHiringPost({
                    ownerId: interaction.user.id,
                    channelId: thread.id,
                    messageId: thread.messages.cache.first().id,
                    title,
                    description,
                    createdTimestamp: interaction.createdTimestamp
                })

                return interaction.editReply({ content: `Your post has been created: ${thread}`, embeds: [], components: [] })
            } catch {
                //TODO Error handling
            }
        }
    }
})