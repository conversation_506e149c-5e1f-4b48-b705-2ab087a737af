import { Events } from "discord.js";
import { readdirSync } from "fs"
import path from "path"
import log from "./log";
import chalk from "chalk";
import Event from "../../structures/Event";

/** @param {import('../../structures/Client').default} client */
export default (client) => {
    const _path = path.join(process.cwd(), 'src', 'events');
    const files = readdirSync(path.join(_path)).filter(f => f.endsWith('.js'));

    for (const file of files) {
        const event = require(path.join(_path, file)).default;

        if (!(event instanceof Event)) {
            log('e', `Invalid event file: ${chalk.underline.redBright(`src/events/${file}`)}`);
            process.exit(1)
        }
        if (!Object.values(Events).includes(event.name)) {
            log('e', `Invalid event name: ${chalk.redBright(event.name)}  ——  ${chalk.underline.redBright(`src/events/${file}`)}`);
            process.exit(1);
        }
        if (typeof event.execute !== 'function') {
            log('e', `Event has no execute function: ${chalk.underline.redBright(`src/events/${file}`)}`);
            process.exit(1);
        }

        if (event.once) {
            client.once(event.name, (...args) => event.execute(client, ...args));
        } else {
            client.on(event.name, (...args) => event.execute(client, ...args));
        }
    }
}