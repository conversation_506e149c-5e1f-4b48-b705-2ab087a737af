import { Con<PERSON>er<PERSON><PERSON>er, EmbedBuilder, MessageFlags, PermissionFlagsBits, SlashCommandBuilder, TextDisplayBuilder } from "discord.js";
import Command from "../structures/Command";
import ms from 'enhanced-ms'
import { newCase } from "../utils/database/main";
import moment from "moment";
import 'moment-duration-format'

export default new Command({
    data: new SlashCommandBuilder()
        .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers)
        .setName('ban')
        .setDescription('Ban a user.')
        .addUserOption(option => option
            .setName('user')
            .setDescription('user to be banned')
            .setRequired(true)
        )
        .addStringOption(option => option
            .setName('reason')
            .setDescription('reason for the ban')
        )
        .addStringOption(option => option
            .setName('duration')
            .setDescription('duration of the ban')
        ),
    async execute(client, interaction) {

        const user = interaction.options.getUser('user', true);

        if (user.id === interaction.user.id) {
            return interaction.reply({ embeds: [client.embed('err', 'You can\'t ban yourself.')], flags: MessageFlags.Ephemeral })
        }

        if (user.id === interaction.guild.ownerId) {
            return interaction.reply({ embeds: [client.embed('perm', 'You can\'t ban the server owner.')], flags: MessageFlags.Ephemeral })
        }

        if (user.id === client.user.id) {
            return interaction.reply({ embeds: [client.embed('err', 'I can\'t ban myself. But why would you want to do that? 🥲')], flags: MessageFlags.Ephemeral })
        }

        const banned = await interaction.guild.bans.fetch({ user, force: true }).catch(() => null);
        if (banned) {
            return interaction.reply({ embeds: [client.embed('err', 'That user is already banned.')], flags: MessageFlags.Ephemeral })
        }

        const member = await interaction.guild.members.fetch({ user, force: true }).catch(() => null);

        if (member && member.roles.highest.position >= interaction.member.roles.highest.position && interaction.member.id !== interaction.guild.ownerId) {
            return interaction.reply({ embeds: [client.embed('perm', 'You can\'t ban someone with a higher or equal role to yours.')], flags: MessageFlags.Ephemeral })
        }

        if (member && !member.bannable) {
            return interaction.reply({ embeds: [client.embed('err', `I am not able to ban that user. Please move ${interaction.guild.roles.botRoleFor(client.user.id)} role to the top.`)], flags: MessageFlags.Ephemeral })
        }

        const reason = interaction.options.getString('reason');
        const durationString = interaction.options.getString('duration');
        const duration = durationString && ms(durationString);

        if (durationString && isNaN(duration)) {
            return interaction.reply({ embeds: [client.embed('err', 'Invalid duration. Please use a valid duration format.')], flags: MessageFlags.Ephemeral })
        }

        if (duration) {
            client.db.push('tempBans', {
                userId: user.id,
                guildId: interaction.guildId,
                endsTimestamp: interaction.createdTimestamp + duration,
            })
        }

        await interaction.guild.bans.create(user, { reason: reason || 'No reason provided.' });
        const now = Date.now()
        const time = now - interaction.createdTimestamp

        const _case = await newCase({
            userId: user.id,
            moderatorId: interaction.user.id,
            type: 'ban',
            reason,
            createdTimestamp: interaction.createdTimestamp,
            endsTimestamp: duration && interaction.createdTimestamp + duration,
            duration
        })

        moment.locale('en')
        const _duration = moment.duration(duration).format({ trim: 'all' })

        const container = new ContainerBuilder().addTextDisplayComponents(new TextDisplayBuilder().setContent(
            `${client.emoji('ban_hammer')} ${user} has been banned from the server${duration ? ` for *${_duration}*` : ''}.${reason ? `\n\n**${client.emoji('pen')} Reason:** ${reason}` : ''}`
        ))

        interaction.reply({ components: [container], flags: MessageFlags.IsComponentsV2 })

        const embed = new EmbedBuilder()
            .setColor('Red')
            .setAuthor({ name: 'User Banned', iconURL: client.emoji('ban_hammer').imageURL() })
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp(now)
            .setFooter({ text: `This action took ${time.toLocaleString()} ms.` })
            .addFields({ name: 'Case No', value: `#${_case.id.toString().padStart(4, '0')}`, inline: true })
            .addFields({ name: 'Banned User', value: user.toString(), inline: true })
            .addFields({ name: 'Responsible Moderator', value: interaction.user.toString(), inline: true })
            .addFields({ name: 'Duration', value: durationString ? _duration : 'Indefinite', inline: true })
            .addFields({ name: 'Ends At', value: duration ? `<t:${Math.floor((interaction.createdTimestamp + duration) / 1000)}:F>` : 'Never', inline: true })
            .addFields({ name: 'Reason', value: reason ?? 'No reason provided.', inline: true })

        const mainLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.mainLog'));
        const banLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.banLog'));

        if (mainLog) {
            mainLog.send({ embeds: [embed] })
        }
        if (banLog) {
            banLog.send({ embeds: [embed] })
        }

    }
})