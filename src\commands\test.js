import { ActionRowBuilder, <PERSON>tonBuilder, ButtonStyle, ContainerBuilder, MediaGalleryBuilder, MediaGalleryItemBuilder, MessageFlags, SeparatorBuilder, SeparatorSpacingSize, SlashCommandBuilder, TextDisplayBuilder } from "discord.js";
import Command from "../structures/Command";

export default new Command({
    data: new SlashCommandBuilder()
        .setName('test')
        .setDescription('...'),
    cooldown: 10,
    execute(client, interaction) {

        const contaienr = new ContainerBuilder()
            .addMediaGalleryComponents(
                new MediaGalleryBuilder()
                    .addItems(
                        new MediaGalleryItemBuilder()
                            .setURL('https://cdn.discordapp.com/attachments/1390439916063690974/1390699899992936570/Support_Need_Help.png?ex=686935b6&is=6867e436&hm=9438691347fb3ee3ed7514ce74db67c8c08f858f32804dc2a54a23d0051c64a4&')
                    )
            )
            .addSeparatorComponents(
                new SeparatorBuilder()
                    .setSpacing(SeparatorSpacingSize.Large)
            )
            .addTextDisplayComponents(
                new TextDisplayBuilder()
                    .setContent(`
If you're facing any issues or have a question, **feel free to create a support ticket.**

${client.emoji('cooldown')} Our moderator team will respond as soon as possible.
`)
            )
            .addSeparatorComponents(
                new SeparatorBuilder()
                    .setSpacing(SeparatorSpacingSize.Large)
            )
            .addTextDisplayComponents(
                new TextDisplayBuilder()
                    .setContent(`
${client.emoji('rules')} **Ticket Rules**
- Do not ping moderators.
- Do not spam or misuse the ticket system.
- Explain your issue clearly and concisely.
`)
            )

        const button = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setStyle(ButtonStyle.Primary)
                    .setLabel('Create Ticket')
                    .setEmoji('🎫')
                    .setCustomId('create-ticket')
            )

        interaction.channel.send({ components: [contaienr, button], flags: MessageFlags.IsComponentsV2 })

        //         const container = new ContainerBuilder()
        //             .addMediaGalleryComponents(
        //                 new MediaGalleryBuilder()
        //                     .addItems(
        //                         new MediaGalleryItemBuilder()
        //                             .setURL('https://cdn.discordapp.com/attachments/1390439916063690974/1390439985802379274/How_to_create_a_forum_post.png?ex=686843a5&is=6866f225&hm=ff30256a596e7b2f5884a82d6029f77d111afaa1f707ff7f71b22909c52ebbf6&')
        //                     )
        //             )
        //             .addSeparatorComponents(
        //                 new SeparatorBuilder()
        //                     .setSpacing(SeparatorSpacingSize.Large)
        //             )
        //             .addTextDisplayComponents(
        //                 new TextDisplayBuilder()
        //                     .setContent(`
        // 👋 Welcome to the **Hiring Forum**!

        // Here, you can **hire people** for your project.

        // To create a post, **click the button below.**
        // `)
        //             )
        //             .addSeparatorComponents(
        //                 new SeparatorBuilder()
        //                     .setSpacing(SeparatorSpacingSize.Large)
        //             )
        //             .addTextDisplayComponents(
        //                 new TextDisplayBuilder()
        //                     .setContent(`
        // ${client.emoji('rules')} **Forum Rules**
        // - Do not spam or misuse this forum.
        // - Scams or troll posts will not be tolerated.
        // - Respect applicants and keep it professional.
        // `)
        //             )
        //             .addSeparatorComponents(
        //                 new SeparatorBuilder()
        //                     .setSpacing(SeparatorSpacingSize.Large)
        //             )
        //             .addTextDisplayComponents(
        //                 new TextDisplayBuilder()
        //                     .setContent('💼 Good luck finding your next team member!')
        //             )

        //         const button = new ActionRowBuilder()
        //             .addComponents(
        //                 new ButtonBuilder()
        //                     .setStyle(ButtonStyle.Secondary)
        //                     .setCustomId('hiring-new-post')
        //                     .setLabel('Create Post')
        //                     .setEmoji('🧑‍💼')
        //             )

        //         const forum = interaction.guild.channels.cache.get('1390415462948405350')
        //         forum.threads.create({
        //             name: 'How to create a post?',
        //             message: { components: [container, button], flags: MessageFlags.IsComponentsV2 }
        //         })

    }
})