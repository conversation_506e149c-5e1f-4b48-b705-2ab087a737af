export default class Command {

    /**
     * @param {object} options 
     * @param {import('discord.js').SlashCommandBuilder} options.data
     * @param {boolean?} options.ownersOnly - Yalnızca botun sahipleri kullanabilir.
     * @param {number?} options.cooldown - Saniye cinsinden cooldown süresi
     * @param {(client: import('./Client').default, interaction: import('discord.js').ChatInputCommandInteraction) => void} options.execute
     */
    constructor(options = {}) {
        this.data = options.data;
        this.ownersOnly = Boolean(options.ownersOnly);
        this.cooldown = (options.cooldown || 0) * 1000;
        this.execute = options.execute;
    }
}