import { readdirSync } from "fs"
import path from "path"
import log from "./log";
import chalk from "chalk";
import Command from "../../structures/Command";

/** @param {import('../../structures/Client').default} client */
export default (client) => {
    const _path = path.join(process.cwd(), 'src', 'commands');
    const files = readdirSync(path.join(_path)).filter(f => f.endsWith('.js'));

    for (const file of files) {
        const command = require(path.join(_path, file)).default;

        if (!(command instanceof Command)) {
            log('e', `Invalid command file: ${chalk.underline.redBright(`src/commands/${file}`)}`);
            process.exit(1)
        }
        if (!command.data || typeof command.data !== 'object') {
            log('e', `Command has no ${chalk.yellowBright('data')} property: ${chalk.underline.redBright(`src/commands/${file}`)}`);
            process.exit(1);
        }
        if (!command.data.name || !command.data.description) {
            log('e', `${chalk.yellowBright('data')} property is invalid: ${chalk.underline.redBright(`src/commands/${file}`)}`);
            process.exit(1);

        }
        if (!command.execute || typeof command.execute !== 'function') {
            log('e', `Command has no ${chalk.yellowBright('execute')} function: ${chalk.underline.redBright(`src/commands/${file}`)}`);
            process.exit(1);
        }

        client.commands.set(command.data.name, command);
    }
}