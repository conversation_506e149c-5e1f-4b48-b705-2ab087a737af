import { ContainerBuilder, EmbedBuilder, MessageFlags, PermissionFlagsBits, SlashCommandBuilder, TextDisplayBuilder } from "discord.js";
import Command from "../structures/Command";
import { newCase } from "../utils/database/main";

export default new Command({
    data: new SlashCommandBuilder()
        .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers)
        .setName('warn')
        .setDescription('Warn a user.')
        .addUserOption(option => option
            .setName('user')
            .setDescription('user to be warned')
            .setRequired(true)
        )
        .addStringOption(option => option
            .setName('reason')
            .setDescription('reason for the warning')
        ),
    async execute(client, interaction) {

        const user = interaction.options.getUser('user', true);

        if (user.id === interaction.user.id) {
            return interaction.reply({ embeds: [client.embed('err', 'You can\'t warn yourself.')], flags: MessageFlags.Ephemeral })
        }

        if (user.id === interaction.guild.ownerId) {
            return interaction.reply({ embeds: [client.embed('perm', 'You can\'t warn the server owner.')], flags: MessageFlags.Ephemeral })
        }

        if (user.id === client.user.id) {
            return interaction.reply({ embeds: [client.embed('err', 'I can\'t warn myself. But why would you want to do that? 🥲')], flags: MessageFlags.Ephemeral })
        }

        const member = await interaction.guild.members.fetch({ user, force: true }).catch(() => null);

        if (!member) {
            return interaction.reply({ embeds: [client.embed('err', 'That user is not in the server.')], flags: MessageFlags.Ephemeral })
        }

        if (member.roles.highest.position >= interaction.member.roles.highest.position && interaction.member.id !== interaction.guild.ownerId) {
            return interaction.reply({ embeds: [client.embed('perm', 'You can\'t warn someone with a higher or equal role to yours.')], flags: MessageFlags.Ephemeral })
        }

        const reason = interaction.options.getString('reason');

        const _case = await newCase({
            userId: user.id,
            moderatorId: interaction.user.id,
            type: 'warn',
            reason,
            createdTimestamp: interaction.createdTimestamp
        })

        const now = Date.now()
        const time = now - interaction.createdTimestamp

        const container = new ContainerBuilder().addTextDisplayComponents(new TextDisplayBuilder().setContent(
            `${client.emoji('warn')} ${user} has been warned.${reason ? `\n\n**${client.emoji('pen')} Reason:** ${reason}` : ''}`
        ))

        interaction.reply({ components: [container], flags: MessageFlags.IsComponentsV2 })

        const embed = new EmbedBuilder()
            .setColor('Yellow')
            .setAuthor({ name: 'User Warned', iconURL: client.emoji('warn').imageURL() })
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp(now)
            .setFooter({ text: `This action took ${time.toLocaleString()} ms.` })
            .addFields({ name: 'Case No', value: `#${_case.id.toString().padStart(4, '0')}`, inline: true })
            .addFields({ name: 'Warned User', value: user.toString(), inline: true })
            .addFields({ name: 'Responsible Moderator', value: interaction.user.toString(), inline: true })
            .addFields({ name: 'Reason', value: reason ?? 'No reason provided.', inline: true })

        const mainLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.mainLog'));
        const warnLog = interaction.guild.channels.cache.get(client.db.get('settings.channels.warnLog'));

        if (mainLog) {
            mainLog.send({ embeds: [embed] })
        }
        if (warnLog) {
            warnLog.send({ embeds: [embed] })
        }

    }
})