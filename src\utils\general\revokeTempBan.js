import { EmbedBuilder } from "discord.js";
import { newCase } from "../database/main";
import log from "./log";

/**
 * @param {import('../../structures/Client').default} client
 * @param {{ userId: string; guildId: string; endsTimestamp: number; }} data
 */
export default async (client, data) => {
    try {
        const guild = await client.guilds.fetch(data.guildId);
        const reason = 'Temporary ban expired.'
        const user = await guild.bans.remove(data.userId, reason)
        client.db.set('tempBans', client.db.get('tempBans').filter(d => !(d.userId == data.userId && d.guildId == data.guildId)))
        const _case = await newCase({
            type: 'unban',
            createdTimestamp: data.endsTimestamp,
            moderatorId: client.user.id,
            userId: data.userId,
            reason
        })

        const embed = new EmbedBuilder()
            .setColor('Grey')
            .setAuthor({ name: 'Ban Revoked', iconURL: client.emoji('unban_hammer').imageURL() })
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp()
            .addFields({ name: 'Case No', value: `#${_case.id.toString().padStart(4, '0')}`, inline: true })
            .addFields({ name: 'User', value: user.toString(), inline: true })
            .addFields({ name: 'Responsible Moderator', value: client.user.toString(), inline: true })
            .addFields({ name: 'Reason', value: reason, inline: true })

        const mainLog = await guild.channels.fetch(client.db.get('settings.channels.mainLog'));
        const banLog = await guild.channels.fetch(client.db.get('settings.channels.banLog'));

        if (mainLog) {
            mainLog.send({ embeds: [embed] })
        }
        if (banLog) {
            banLog.send({ embeds: [embed] })
        }
    } catch (error) {
        log('e', `Failed to revoke a temp ban:`)
        console.log(error.stack)
    }
}