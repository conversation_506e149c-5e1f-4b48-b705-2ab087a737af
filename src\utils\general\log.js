import chalk from "chalk";

/**
 * @param {'i' | 'w' | 'e' | 'o' | 'ok'} type
 * @param {string[]} msg
 */
export default (type, ...msg) => {
    const types = {
        i: `[  ${chalk.blueBright('INFO')}  ]`,
        w: `[  ${chalk.yellowBright('WARN')}  ]`,
        e: `[  ${chalk.redBright('ERR')}  ]`,
        o: `[  ${chalk.greenBright('OK')}  ]`,
        ok: `[  ${chalk.greenBright('OK')}  ]`
    }

    console.log(`[  ${chalk.grey(new Date().toLocaleTimeString())}  ]  ${types[type]}  ${msg.join(' ')}`)
}