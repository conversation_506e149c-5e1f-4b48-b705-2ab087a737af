/** @template {keyof import("discord.js").ClientEvents} K */
export default class Event {

    /**
     * @param {object} options 
     * @param {K} options.name
     * @param {boolean?} options.once
     * @param {(client: import('./Client').default, ...args: import("discord.js").ClientEvents[K]) => void} options.execute
     */
    constructor(options = {}) {
        this.name = options.name;
        this.once = Boolean(options.once);
        this.execute = options.execute;
    }
}